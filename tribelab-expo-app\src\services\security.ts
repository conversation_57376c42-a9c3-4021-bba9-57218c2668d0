import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

export interface SecurityConfig {
  maxLoginAttempts: number;
  lockoutDurationMinutes: number;
  passwordMinLength: number;
  requirePasswordComplexity: boolean;
  sessionTimeoutMinutes: number;
}

export class SecurityService {
  private static instance: SecurityService;
  private config: SecurityConfig;

  private constructor() {
    this.config = {
      maxLoginAttempts: 5,
      lockoutDurationMinutes: 15,
      passwordMinLength: 8,
      requirePasswordComplexity: true,
      sessionTimeoutMinutes: 30 * 24 * 60, // 30 days in minutes
    };
  }

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  // Login attempt tracking
  public async trackLoginAttempt(email: string, success: boolean): Promise<void> {
    const key = `login_attempts_${email}`;
    
    if (success) {
      // Clear failed attempts on successful login
      await AsyncStorage.removeItem(key);
      await AsyncStorage.removeItem(`lockout_${email}`);
    } else {
      // Increment failed attempts
      const attemptsData = await AsyncStorage.getItem(key);
      const attempts = attemptsData ? JSON.parse(attemptsData) : { count: 0, lastAttempt: Date.now() };
      
      attempts.count += 1;
      attempts.lastAttempt = Date.now();
      
      await AsyncStorage.setItem(key, JSON.stringify(attempts));
      
      // Check if user should be locked out
      if (attempts.count >= this.config.maxLoginAttempts) {
        const lockoutUntil = Date.now() + (this.config.lockoutDurationMinutes * 60 * 1000);
        await AsyncStorage.setItem(`lockout_${email}`, lockoutUntil.toString());
      }
    }
  }

  public async isAccountLocked(email: string): Promise<{ locked: boolean; unlockTime?: Date }> {
    const lockoutData = await AsyncStorage.getItem(`lockout_${email}`);
    
    if (!lockoutData) {
      return { locked: false };
    }
    
    const lockoutUntil = parseInt(lockoutData);
    const now = Date.now();
    
    if (now < lockoutUntil) {
      return { locked: true, unlockTime: new Date(lockoutUntil) };
    } else {
      // Lockout period has expired, clear it
      await AsyncStorage.removeItem(`lockout_${email}`);
      await AsyncStorage.removeItem(`login_attempts_${email}`);
      return { locked: false };
    }
  }

  public async getFailedLoginAttempts(email: string): Promise<number> {
    const attemptsData = await AsyncStorage.getItem(`login_attempts_${email}`);
    if (!attemptsData) return 0;
    
    const attempts = JSON.parse(attemptsData);
    return attempts.count || 0;
  }

  // Password validation
  public validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < this.config.passwordMinLength) {
      errors.push(`Password must be at least ${this.config.passwordMinLength} characters long`);
    }
    
    if (this.config.requirePasswordComplexity) {
      if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      
      if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      
      if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
      }
      
      if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        errors.push('Password must contain at least one special character');
      }
    }
    
    return { valid: errors.length === 0, errors };
  }

  // Email validation
  public validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Username validation
  public validateUsername(username: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (username.length < 3) {
      errors.push('Username must be at least 3 characters long');
    }
    
    if (username.length > 20) {
      errors.push('Username must be no more than 20 characters long');
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
      errors.push('Username can only contain letters, numbers, and underscores');
    }
    
    return { valid: errors.length === 0, errors };
  }

  // Data encryption/decryption
  public async encryptData(data: string): Promise<string> {
    try {
      // For demo purposes, using base64 encoding
      // In production, use proper encryption
      return Buffer.from(data).toString('base64');
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  public async decryptData(encryptedData: string): Promise<string> {
    try {
      // For demo purposes, using base64 decoding
      // In production, use proper decryption
      return Buffer.from(encryptedData, 'base64').toString();
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  // Generate secure random strings
  public async generateSecureToken(length: number = 32): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(length);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Device fingerprinting (basic)
  public async getDeviceFingerprint(): Promise<string> {
    const deviceInfo = {
      platform: Platform.OS,
      version: Platform.Version,
      timestamp: Date.now(),
    };
    
    const fingerprint = JSON.stringify(deviceInfo);
    return await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, fingerprint);
  }

  // Security headers for API requests
  public getSecurityHeaders(): Record<string, string> {
    return {
      'X-Requested-With': 'XMLHttpRequest',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
    };
  }

  // Rate limiting (client-side)
  private rateLimitStore: Map<string, { count: number; resetTime: number }> = new Map();

  public checkRateLimit(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const record = this.rateLimitStore.get(key);
    
    if (!record || now > record.resetTime) {
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (record.count >= maxRequests) {
      return false;
    }
    
    record.count++;
    return true;
  }

  // Clear security data
  public async clearSecurityData(): Promise<void> {
    const keys = await AsyncStorage.getAllKeys();
    const securityKeys = keys.filter(key => 
      key.startsWith('login_attempts_') || 
      key.startsWith('lockout_') ||
      key.startsWith('security_')
    );
    
    if (securityKeys.length > 0) {
      await AsyncStorage.multiRemove(securityKeys);
    }
    
    this.rateLimitStore.clear();
  }

  // Update security configuration
  public updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): SecurityConfig {
    return { ...this.config };
  }
}

export const securityService = SecurityService.getInstance();
