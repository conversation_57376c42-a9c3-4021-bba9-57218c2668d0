import { sessionManager } from '../../services/sessionManager';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState } from 'react-native';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
}));

// Mock AppState
jest.mock('react-native', () => ({
  AppState: {
    addEventListener: jest.fn(),
  },
}));

describe('SessionManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Last Activity Tracking', () => {
    it('should update last activity timestamp', async () => {
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      await sessionManager.updateLastActivity();

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'last_activity',
        expect.any(String)
      );
    });

    it('should retrieve last activity timestamp', async () => {
      const testDate = new Date('2023-01-01T12:00:00Z');
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(testDate.toISOString());

      const result = await sessionManager.getLastActivity();

      expect(result).toEqual(testDate);
    });

    it('should return null when no last activity exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      const result = await sessionManager.getLastActivity();

      expect(result).toBeNull();
    });
  });

  describe('Session Expiry', () => {
    it('should detect expired sessions', async () => {
      // Set last activity to 31 days ago
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 31);
      
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(expiredDate.toISOString());

      const result = await sessionManager.isSessionExpired();

      expect(result).toBe(true);
    });

    it('should detect valid sessions', async () => {
      // Set last activity to 1 day ago
      const validDate = new Date();
      validDate.setDate(validDate.getDate() - 1);
      
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(validDate.toISOString());

      const result = await sessionManager.isSessionExpired();

      expect(result).toBe(false);
    });

    it('should consider session expired when no last activity exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      const result = await sessionManager.isSessionExpired();

      expect(result).toBe(true);
    });
  });

  describe('Token Expiry Checking', () => {
    it('should detect expired tokens', async () => {
      // Create a token that expired 1 hour ago
      const expiredTime = Math.floor(Date.now() / 1000) - 3600;
      const tokenPayload = { exp: expiredTime };
      const token = `header.${btoa(JSON.stringify(tokenPayload))}.signature`;

      const result = await sessionManager.isTokenExpired(token);

      expect(result).toBe(true);
    });

    it('should detect valid tokens', async () => {
      // Create a token that expires in 1 hour
      const futureTime = Math.floor(Date.now() / 1000) + 3600;
      const tokenPayload = { exp: futureTime };
      const token = `header.${btoa(JSON.stringify(tokenPayload))}.signature`;

      const result = await sessionManager.isTokenExpired(token);

      expect(result).toBe(false);
    });

    it('should detect tokens expiring soon', async () => {
      // Create a token that expires in 30 minutes (less than 1 hour threshold)
      const soonTime = Math.floor(Date.now() / 1000) + 1800;
      const tokenPayload = { exp: soonTime };
      const token = `header.${btoa(JSON.stringify(tokenPayload))}.signature`;

      const result = await sessionManager.isTokenExpiringSoon(token);

      expect(result).toBe(true);
    });

    it('should handle invalid tokens gracefully', async () => {
      const invalidToken = 'invalid.token.format';

      const expiredResult = await sessionManager.isTokenExpired(invalidToken);
      const expiringSoonResult = await sessionManager.isTokenExpiringSoon(invalidToken);

      expect(expiredResult).toBe(true);
      expect(expiringSoonResult).toBe(true);
    });
  });

  describe('Session Clearing', () => {
    it('should clear all session data', async () => {
      (AsyncStorage.multiRemove as jest.Mock).mockResolvedValue(undefined);

      await sessionManager.clearSession();

      expect(AsyncStorage.multiRemove).toHaveBeenCalledWith([
        'auth_token',
        'refresh_token',
        'last_activity',
        'user_data'
      ]);
    });
  });

  describe('Configuration Management', () => {
    it('should return current configuration', () => {
      const config = sessionManager.getSessionConfig();

      expect(config).toHaveProperty('inactivityTimeoutDays');
      expect(config).toHaveProperty('tokenRefreshThresholdMinutes');
      expect(config).toHaveProperty('autoLogoutOnExpiry');
      expect(config.inactivityTimeoutDays).toBe(30);
    });

    it('should update configuration', () => {
      const newConfig = { inactivityTimeoutDays: 60 };
      
      sessionManager.updateSessionConfig(newConfig);
      const updatedConfig = sessionManager.getSessionConfig();

      expect(updatedConfig.inactivityTimeoutDays).toBe(60);
    });
  });

  describe('Activity Tracking', () => {
    it('should track user activity', async () => {
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      sessionManager.trackUserActivity();

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'last_activity',
        expect.any(String)
      );
    });
  });
});

// Helper function to create base64 encoded JWT payload
function btoa(str: string): string {
  return Buffer.from(str).toString('base64');
}
