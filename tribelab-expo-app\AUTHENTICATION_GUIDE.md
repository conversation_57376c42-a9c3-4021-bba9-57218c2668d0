# TribeLab Mobile App Authentication System

## Overview

This document describes the comprehensive authentication system implemented in the TribeLab Expo mobile app, which mirrors the exact functionality of the website's authentication system.

## Features Implemented

### ✅ Core Authentication
- **User Registration** with username, email, and password
- **User Login** with email and password
- **Google OAuth Authentication** for both login and registration
- **Email Verification** system with token-based verification
- **Session Management** with 1-month inactivity timeout
- **Token Refresh** mechanism for seamless user experience

### ✅ Security Features
- **Password Strength Validation** (uppercase, lowercase, numbers, special characters)
- **Email Format Validation**
- **Username Format Validation** (3-20 characters, alphanumeric + underscore)
- **Login Attempt Tracking** with account lockout after 5 failed attempts
- **Rate Limiting** to prevent brute force attacks
- **Session Timeout** after 30 days of inactivity
- **Secure Token Generation** for various authentication flows

### ✅ User Experience
- **Biometric Authentication** support (fingerprint/face recognition)
- **Auto-logout** on session expiry
- **Seamless Navigation** based on authentication state
- **Error Handling** with user-friendly messages
- **Loading States** for all authentication operations

## Architecture

### Authentication Flow
```
User Input → Security Validation → API Call → Token Storage → Session Management
```

### Key Components

#### 1. Authentication Slice (`src/store/slices/authSlice.ts`)
- Redux state management for authentication
- Async thunks for login, register, Google auth, email verification
- Session persistence and restoration

#### 2. Security Service (`src/services/security.ts`)
- Password validation with complexity requirements
- Email and username validation
- Login attempt tracking and account lockout
- Rate limiting and security headers
- Data encryption utilities

#### 3. Session Manager (`src/services/sessionManager.ts`)
- 1-month inactivity timeout tracking
- Token expiry detection and refresh
- App state monitoring for session management
- Activity tracking across the app

#### 4. Google Auth Service (`src/services/googleAuth.ts`)
- Google OAuth 2.0 implementation
- Cross-platform support (iOS, Android, Web)
- User profile retrieval from Google

#### 5. Auth Guard (`src/components/AuthGuard.tsx`)
- Route protection based on authentication status
- Email verification requirements
- Role-based access control
- Automatic redirects for unauthorized access

## Configuration

### Environment Variables
```env
# Google OAuth Configuration
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your-ios-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID=your-android-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your-web-client-id.apps.googleusercontent.com
```

### Security Configuration
```typescript
const securityConfig = {
  maxLoginAttempts: 5,
  lockoutDurationMinutes: 15,
  passwordMinLength: 8,
  requirePasswordComplexity: true,
  sessionTimeoutMinutes: 30 * 24 * 60, // 30 days
};
```

## Usage Examples

### Protecting Screens
```typescript
import AuthGuard from '../components/AuthGuard';

const ProtectedScreen = () => (
  <AuthGuard requireEmailVerification={true}>
    <YourScreenContent />
  </AuthGuard>
);
```

### Using Session Management
```typescript
import { useSessionManager } from '../hooks/useSessionManager';

const MyComponent = () => {
  const { trackActivity, forceSessionCheck } = useSessionManager();
  
  // Track user activity
  const handleUserInteraction = () => {
    trackActivity();
  };
  
  return <YourComponent onPress={handleUserInteraction} />;
};
```

### Security Validation
```typescript
import { securityService } from '../services/security';

// Validate password
const passwordValidation = securityService.validatePassword(password);
if (!passwordValidation.valid) {
  console.log('Errors:', passwordValidation.errors);
}

// Check account lockout
const lockStatus = await securityService.isAccountLocked(email);
if (lockStatus.locked) {
  console.log('Account locked until:', lockStatus.unlockTime);
}
```

## Testing

### Running Authentication Tests
```bash
# Run all authentication tests
npm run test:auth

# Run specific test suites
npm test src/__tests__/services/security.test.ts
npm test src/__tests__/services/sessionManager.test.ts
npm test src/__tests__/integration/auth.integration.test.tsx
```

### Test Coverage
- **Security Service**: Password validation, email validation, login tracking
- **Session Manager**: Session expiry, token management, activity tracking
- **Integration Tests**: Complete authentication flows with security features
- **Auth Guard**: Route protection and access control

## Security Considerations

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Account Protection
- Maximum 5 failed login attempts
- 15-minute lockout period after max attempts
- Rate limiting: 5 requests per minute per email
- Session timeout after 30 days of inactivity

### Data Protection
- Tokens stored securely in AsyncStorage
- Sensitive data encrypted before storage
- Automatic token refresh before expiry
- Secure headers for API requests

## Troubleshooting

### Common Issues

1. **Google OAuth not working**
   - Verify client IDs in environment variables
   - Check app.json configuration
   - Ensure proper URL schemes are set

2. **Session expiring too quickly**
   - Check session timeout configuration
   - Verify activity tracking is working
   - Review token refresh mechanism

3. **Account lockout issues**
   - Check failed login attempt tracking
   - Verify lockout duration settings
   - Review rate limiting configuration

### Debug Mode
Enable debug logging by setting:
```env
DEBUG_AUTH=true
```

## Migration from Website

The mobile app authentication system is designed to be fully compatible with the website:

- **Same User Database**: Uses the same MongoDB Atlas database
- **Compatible Tokens**: JWT tokens work across both platforms
- **Shared Sessions**: Users can switch between web and mobile seamlessly
- **Consistent Validation**: Same password and email validation rules

## Future Enhancements

- [ ] Two-factor authentication (2FA)
- [ ] Social login with Facebook, Apple
- [ ] Advanced biometric options
- [ ] Device management and trusted devices
- [ ] Advanced security analytics

## Support

For authentication-related issues:
1. Check the troubleshooting section above
2. Review the test results with `npm run test:auth`
3. Enable debug mode for detailed logging
4. Consult the security service documentation
