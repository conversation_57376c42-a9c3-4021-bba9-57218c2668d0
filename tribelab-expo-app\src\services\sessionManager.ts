import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';

export interface SessionConfig {
  inactivityTimeoutDays: number;
  tokenRefreshThresholdMinutes: number;
  autoLogoutOnExpiry: boolean;
}

export class SessionManager {
  private static instance: SessionManager;
  private appStateSubscription: any;
  private sessionConfig: SessionConfig;
  private lastActivityTime: Date;
  private sessionCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.sessionConfig = {
      inactivityTimeoutDays: 30, // 1 month as per requirement
      tokenRefreshThresholdMinutes: 60, // Refresh token 1 hour before expiry
      autoLogoutOnExpiry: true,
    };
    this.lastActivityTime = new Date();
    this.initializeAppStateListener();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  private initializeAppStateListener() {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  private handleAppStateChange(nextAppState: AppStateStatus) {
    if (nextAppState === 'active') {
      // App came to foreground, update last activity
      this.updateLastActivity();
      this.startSessionCheck();
    } else if (nextAppState === 'background' || nextAppState === 'inactive') {
      // App went to background, stop session checks
      this.stopSessionCheck();
    }
  }

  public async updateLastActivity(): Promise<void> {
    this.lastActivityTime = new Date();
    await AsyncStorage.setItem('last_activity', this.lastActivityTime.toISOString());
  }

  public async getLastActivity(): Promise<Date | null> {
    try {
      const lastActivityString = await AsyncStorage.getItem('last_activity');
      return lastActivityString ? new Date(lastActivityString) : null;
    } catch (error) {
      console.error('Error getting last activity:', error);
      return null;
    }
  }

  public async isSessionExpired(): Promise<boolean> {
    const lastActivity = await this.getLastActivity();
    if (!lastActivity) {
      return true; // No last activity means session is expired
    }

    const now = new Date();
    const daysSinceLastActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24);
    
    return daysSinceLastActivity > this.sessionConfig.inactivityTimeoutDays;
  }

  public async clearSession(): Promise<void> {
    await AsyncStorage.multiRemove([
      'auth_token',
      'refresh_token',
      'last_activity',
      'user_data'
    ]);
  }

  public async isTokenExpiringSoon(token: string): Promise<boolean> {
    try {
      const tokenData = JSON.parse(atob(token.split('.')[1]));
      const expiryTime = tokenData.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();
      const timeUntilExpiry = expiryTime - currentTime;
      const thresholdTime = this.sessionConfig.tokenRefreshThresholdMinutes * 60 * 1000;

      return timeUntilExpiry <= thresholdTime;
    } catch (error) {
      console.error('Error checking token expiry:', error);
      return true; // Assume expired if we can't parse
    }
  }

  public async isTokenExpired(token: string): Promise<boolean> {
    try {
      const tokenData = JSON.parse(atob(token.split('.')[1]));
      const expiryTime = tokenData.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();

      return currentTime >= expiryTime;
    } catch (error) {
      console.error('Error checking token expiry:', error);
      return true; // Assume expired if we can't parse
    }
  }

  private startSessionCheck() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
    }

    // Check session every 5 minutes when app is active
    this.sessionCheckInterval = setInterval(async () => {
      const isExpired = await this.isSessionExpired();
      if (isExpired && this.sessionConfig.autoLogoutOnExpiry) {
        // Emit session expired event or call logout
        this.handleSessionExpired();
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  private stopSessionCheck() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  private handleSessionExpired() {
    // This should trigger logout in the app
    // You can emit an event or call a callback here
    console.log('Session expired due to inactivity');
    // For now, just clear the session
    this.clearSession();
  }

  public destroy() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.stopSessionCheck();
  }

  // Activity tracking methods
  public trackUserActivity() {
    this.updateLastActivity();
  }

  public getSessionConfig(): SessionConfig {
    return { ...this.sessionConfig };
  }

  public updateSessionConfig(config: Partial<SessionConfig>) {
    this.sessionConfig = { ...this.sessionConfig, ...config };
  }
}

export const sessionManager = SessionManager.getInstance();
