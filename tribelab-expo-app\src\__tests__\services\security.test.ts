import { securityService } from '../../services/security';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
  getAllKeys: jest.fn(),
}));

describe('SecurityService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    securityService.clearSecurityData();
  });

  describe('Password Validation', () => {
    it('should validate strong passwords', () => {
      const result = securityService.validatePassword('StrongPass123!');
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak passwords', () => {
      const result = securityService.validatePassword('weak');
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should require minimum length', () => {
      const result = securityService.validatePassword('Short1!');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should require uppercase letters', () => {
      const result = securityService.validatePassword('lowercase123!');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should require lowercase letters', () => {
      const result = securityService.validatePassword('UPPERCASE123!');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should require numbers', () => {
      const result = securityService.validatePassword('NoNumbers!');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should require special characters', () => {
      const result = securityService.validatePassword('NoSpecial123');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character');
    });
  });

  describe('Email Validation', () => {
    it('should validate correct email formats', () => {
      expect(securityService.validateEmail('<EMAIL>')).toBe(true);
      expect(securityService.validateEmail('<EMAIL>')).toBe(true);
      expect(securityService.validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email formats', () => {
      expect(securityService.validateEmail('invalid-email')).toBe(false);
      expect(securityService.validateEmail('test@')).toBe(false);
      expect(securityService.validateEmail('@example.com')).toBe(false);
      expect(securityService.validateEmail('test.example.com')).toBe(false);
    });
  });

  describe('Username Validation', () => {
    it('should validate correct usernames', () => {
      const result = securityService.validateUsername('validuser123');
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject short usernames', () => {
      const result = securityService.validateUsername('ab');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Username must be at least 3 characters long');
    });

    it('should reject long usernames', () => {
      const result = securityService.validateUsername('a'.repeat(21));
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Username must be no more than 20 characters long');
    });

    it('should reject usernames with invalid characters', () => {
      const result = securityService.validateUsername('user@name');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Username can only contain letters, numbers, and underscores');
    });
  });

  describe('Login Attempt Tracking', () => {
    const testEmail = '<EMAIL>';

    it('should track failed login attempts', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      await securityService.trackLoginAttempt(testEmail, false);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        `login_attempts_${testEmail}`,
        expect.stringContaining('"count":1')
      );
    });

    it('should clear attempts on successful login', async () => {
      (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);

      await securityService.trackLoginAttempt(testEmail, true);

      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(`login_attempts_${testEmail}`);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(`lockout_${testEmail}`);
    });

    it('should lock account after max attempts', async () => {
      const attempts = { count: 4, lastAttempt: Date.now() };
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(attempts));
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

      await securityService.trackLoginAttempt(testEmail, false);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        `lockout_${testEmail}`,
        expect.any(String)
      );
    });
  });

  describe('Account Lockout', () => {
    const testEmail = '<EMAIL>';

    it('should return unlocked status when no lockout exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      const result = await securityService.isAccountLocked(testEmail);

      expect(result.locked).toBe(false);
    });

    it('should return locked status when lockout is active', async () => {
      const futureTime = Date.now() + 60000; // 1 minute in future
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(futureTime.toString());

      const result = await securityService.isAccountLocked(testEmail);

      expect(result.locked).toBe(true);
      expect(result.unlockTime).toBeInstanceOf(Date);
    });

    it('should clear expired lockouts', async () => {
      const pastTime = Date.now() - 60000; // 1 minute in past
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(pastTime.toString());
      (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);

      const result = await securityService.isAccountLocked(testEmail);

      expect(result.locked).toBe(false);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(`lockout_${testEmail}`);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within limit', () => {
      const result = securityService.checkRateLimit('test-key', 5, 60000);
      expect(result).toBe(true);
    });

    it('should block requests exceeding limit', () => {
      // Make 5 requests (at limit)
      for (let i = 0; i < 5; i++) {
        securityService.checkRateLimit('test-key', 5, 60000);
      }

      // 6th request should be blocked
      const result = securityService.checkRateLimit('test-key', 5, 60000);
      expect(result).toBe(false);
    });

    it('should reset after window expires', () => {
      // Make requests up to limit
      for (let i = 0; i < 5; i++) {
        securityService.checkRateLimit('test-key', 5, 1); // 1ms window
      }

      // Wait for window to expire
      setTimeout(() => {
        const result = securityService.checkRateLimit('test-key', 5, 60000);
        expect(result).toBe(true);
      }, 2);
    });
  });

  describe('Data Encryption', () => {
    it('should encrypt and decrypt data correctly', async () => {
      const originalData = 'sensitive information';
      
      const encrypted = await securityService.encryptData(originalData);
      expect(encrypted).not.toBe(originalData);
      
      const decrypted = await securityService.decryptData(encrypted);
      expect(decrypted).toBe(originalData);
    });
  });

  describe('Token Generation', () => {
    it('should generate secure tokens of specified length', async () => {
      const token = await securityService.generateSecureToken(16);
      expect(token).toHaveLength(32); // 16 bytes = 32 hex characters
      expect(token).toMatch(/^[a-f0-9]+$/);
    });

    it('should generate different tokens each time', async () => {
      const token1 = await securityService.generateSecureToken();
      const token2 = await securityService.generateSecureToken();
      expect(token1).not.toBe(token2);
    });
  });
});
