import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { checkAuthStatus } from '../store/slices/authSlice';
import { useNavigation } from '@react-navigation/native';
import { sessionManager } from '../services/sessionManager';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireEmailVerification?: boolean;
  allowedRoles?: string[];
  fallbackScreen?: string;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  requireEmailVerification = false,
  allowedRoles = [],
  fallbackScreen = 'Login'
}) => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation();
  const { isAuthenticated, isLoading, user } = useAppSelector((state) => state.auth);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (requireAuth) {
          // Check if session is expired
          const isSessionExpired = await sessionManager.isSessionExpired();
          if (isSessionExpired) {
            navigation.navigate(fallbackScreen as never);
            return;
          }

          // Check authentication status
          if (!isAuthenticated) {
            await dispatch(checkAuthStatus()).unwrap();
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        if (requireAuth) {
          navigation.navigate(fallbackScreen as never);
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkAuth();
  }, [dispatch, requireAuth, isAuthenticated, navigation, fallbackScreen]);

  useEffect(() => {
    if (!isChecking && !isLoading && requireAuth) {
      // Check authentication
      if (!isAuthenticated) {
        navigation.navigate(fallbackScreen as never);
        return;
      }

      // Check email verification
      if (requireEmailVerification && user && !user.isEmailVerified) {
        navigation.navigate('VerifyEmail' as never, { email: user.email } as never);
        return;
      }

      // Check user role
      if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
        navigation.navigate('Home' as never);
        return;
      }
    }
  }, [isChecking, isLoading, requireAuth, isAuthenticated, user, requireEmailVerification, allowedRoles, navigation, fallbackScreen]);

  // Show loading while checking authentication
  if (isChecking || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  // If authentication is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  // If email verification is required but user hasn't verified email
  if (requireEmailVerification && user && !user.isEmailVerified) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  // If user doesn't have required role
  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  // Render children if authentication check passes
  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});

export default AuthGuard;
